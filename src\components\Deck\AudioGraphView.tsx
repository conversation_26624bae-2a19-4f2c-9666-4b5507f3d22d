import React from 'react';
// Helper function to get node type name from Web Audio API node
const getNodeTypeName = (node: AudioNode): string => {
  if (node instanceof AudioBufferSourceNode) return 'AudioBufferSourceNode';
  if (node instanceof GainNode) return 'GainNode';
  if (node instanceof BiquadFilterNode) return `BiquadFilterNode(${node.type})`;
  if (node instanceof AnalyserNode) return 'AnalyserNode';
  if (node instanceof AudioWorkletNode) return `AudioWorkletNode(${(node as any).processorName || 'unknown'})`;
  if (node instanceof AudioDestinationNode) return 'AudioDestinationNode';
  return node.constructor.name || 'UnknownNode';
};

// Helper function to get additional info for specific node types
const getAdditionalNodeInfo = (webAudioNode: AudioNode, routingNode: any): string => {
  try {
    if (webAudioNode instanceof GainNode) {
      return `gain: ${webAudioNode.gain.value.toFixed(3)}`;
    }
    if (webAudioNode instanceof BiquadFilterNode) {
      return `freq: ${webAudioNode.frequency.value.toFixed(0)}Hz, gain: ${webAudioNode.gain.value.toFixed(1)}dB`;
    }
    if (webAudioNode instanceof AudioWorkletNode) {
      return `processor: ${(webAudioNode as any).processorName || 'unknown'}`;
    }
    if (routingNode && typeof routingNode.getRMSdB === 'function') {
      const rms = routingNode.getRMSdB();
      const peak = routingNode.getPeakdB();
      return `RMS: ${rms.toFixed(1)}dB, Peak: ${peak.toFixed(1)}dB`;
    }
  } catch (error) {
    // Ignore errors when getting additional info
  }
  return '';
};

// Helper function to inspect actual Web Audio API connections
const inspectAudioGraph = (deckPath: any) => {
  const nodes = [];
  try {
    const routingNodes = deckPath.nodes || [];
    for (let i = 0; i < routingNodes.length; i++) {
      const routingNode = routingNodes[i];
      const webAudioNode = routingNode.getWebAudioNode();
      if (webAudioNode) {
        const nodeInfo = {
          index: i,
          routingNodeType: routingNode.constructor.name,
          webAudioNodeType: getNodeTypeName(webAudioNode),
          webAudioNode: webAudioNode,
          additionalInfo: getAdditionalNodeInfo(webAudioNode, routingNode)
        };
        nodes.push(nodeInfo);
      }
    }
  } catch (error) {
    console.error('Error inspecting audio graph:', error);
  }
  return nodes;
};

// Simple Audio Graph Viewer for Web Audio API
const AudioGraphView: React.FC<{ audioEngine: any }> = ({ audioEngine }) => {
  if (!audioEngine) {
    return (
      <div className="mt-2 p-2 bg-gray-100 rounded text-xs">
        <p><strong>Audio Graph:</strong> No audio engine available</p>
      </div>
    );
  }
  const deckPath = audioEngine.deckPath;
  if (!deckPath) {
    return (
      <div className="mt-2 p-2 bg-gray-100 rounded text-xs">
        <p><strong>Audio Graph:</strong> No deck path initialized</p>
      </div>
    );
  }
  const actualNodes = inspectAudioGraph(deckPath);
  return (
    <div className="mt-2 p-2 bg-gray-100 rounded text-xs">
      <p><strong>Actual Audio Graph:</strong></p>
      <div className="mt-1 font-mono text-xs space-y-1">
        {actualNodes.length === 0 ? (
          <div className="text-gray-500">No nodes found</div>
        ) : (
          actualNodes.map((nodeInfo, index) => (
            <div key={index} className="flex items-center space-x-1">
              <span className="text-blue-600 min-w-[60px]">
                [{nodeInfo.index}]
              </span>
              <span className="text-purple-600 min-w-[120px]">
                {nodeInfo.routingNodeType}
              </span>
              <span>→</span>
              <span className="text-green-600 min-w-[150px]">
                {nodeInfo.webAudioNodeType}
              </span>
              {nodeInfo.additionalInfo && (
                <>
                  <span className="text-gray-400">|</span>
                  <span className="text-orange-600 text-xs">
                    {nodeInfo.additionalInfo}
                  </span>
                </>
              )}
            </div>
          ))
        )}
        <div className="mt-2 pt-2 border-t border-gray-300">
          <div className="text-gray-600">
            Path Connected: {deckPath.isConnected ? '✓' : '✗'}
          </div>
          <div className="text-gray-600">
            Total Nodes: {actualNodes.length}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AudioGraphView;
