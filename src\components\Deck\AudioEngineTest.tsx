// src/components/Deck/AudioEngineTest.tsx
import React, { useEffect } from 'react';
import { observer } from 'mobx-react-lite';
import { DeckStoreInstance } from '@/stores/DeckStore';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Knob } from '@/components/ui/knob';
import { Fader } from '@/components/ui/fader';
import { Play, Pause, SkipBack } from 'lucide-react';
import { useStore } from '@/contexts/StoreContext';
import AudioLevelMeters from '../ui/AudioLevelMeters';
import AudioGraphView from './AudioGraphView';

// Helper function to format time as min:sec.ms
const formatTime = (timeInSeconds: number): string => {
  const minutes = Math.floor(timeInSeconds / 60);
  const seconds = Math.floor(timeInSeconds % 60);
  const milliseconds = Math.floor((timeInSeconds % 1) * 1000);
  return `${minutes}:${seconds.toString().padStart(2, '0')}.${milliseconds.toString().padStart(3, '0')}`;
};

// Extracted component to display current time only
const DeckCurrentTime: React.FC<{ deck: DeckStoreInstance }> = observer(({ deck }) => (
  <p><strong>Current Time:</strong> {formatTime(deck.currentTime)}</p>
));

interface AudioEngineTestProps {
  deck: DeckStoreInstance;
}

const AudioEngineTest: React.FC<AudioEngineTestProps> = observer(({ deck }) => {
  const { settingsStore } = useStore();

  // Determine the minimum EQ value based on the equalizerFullKill setting
  const minEQValue = -12; // The UI always shows -12 as the minimum, but the audio engine will use -Infinity when equalizerFullKill is true

  // Check if we're in 4-band mode
  const is4BandMode = settingsStore.eqBands === "4-band";

  // When the equalizerFullKill setting changes, update the EQ values to apply the new setting
  useEffect(() => {
    if (deck.loadedTrack) {
      // Re-apply current EQ values to trigger the audio engine to check the new setting
      if (is4BandMode) {
        deck.setEQ(deck.lowEQ ?? 0, deck.midEQ ?? 0, deck.highEQ ?? 0, deck.midLoEQ ?? 0, deck.midHiEQ ?? 0);
      } else {
        deck.setEQ(deck.lowEQ ?? 0, deck.midEQ ?? 0, deck.highEQ ?? 0);
      }
    }
  }, [settingsStore.equalizerFullKill, settingsStore.eqBands, deck, deck.loadedTrack, is4BandMode]);
  // Note: Removed local EQ states from dependency array as they no longer exist.
  // The effect now depends on settings and deck state, and uses current values from deck store.

  const handlePlayPause = () => {
    if (deck.isPlaying) {
      deck.pause();
    } else {
      deck.play();
    }
  };
  const handleSeekToStart = () => {
    deck.seek(0);
  };

  const handleLowEQChange = (value: number) => {
    if (is4BandMode) {
      deck.setEQ(value, deck.midEQ ?? 0, deck.highEQ ?? 0, deck.midLoEQ ?? 0, deck.midHiEQ ?? 0);
    } else {
      deck.setEQ(value, deck.midEQ ?? 0, deck.highEQ ?? 0);
    }
  };

  const handleMidEQChange = (value: number) => {
    // This handler is for the 3-band MID knob.
    deck.setEQ(deck.lowEQ ?? 0, value, deck.highEQ ?? 0);
  };

  const handleMidLoEQChange = (value: number) => {
    // This handler is for the 4-band MID-LO knob.
    deck.setEQ(deck.lowEQ ?? 0, deck.midEQ ?? 0, deck.highEQ ?? 0, value, deck.midHiEQ ?? 0);
  };

  const handleMidHiEQChange = (value: number) => {
    // This handler is for the 4-band MID-HI knob.
    deck.setEQ(deck.lowEQ ?? 0, deck.midEQ ?? 0, deck.highEQ ?? 0, deck.midLoEQ ?? 0, value);
  };

  const handleHighEQChange = (value: number) => {
    if (is4BandMode) {
      deck.setEQ(deck.lowEQ ?? 0, deck.midEQ ?? 0, value, deck.midLoEQ ?? 0, deck.midHiEQ ?? 0);
    } else {
      deck.setEQ(deck.lowEQ ?? 0, deck.midEQ ?? 0, value);
    }
  };

  const handleVolumeChange = (value: number) => {
    deck.setVolume(value);
  };

  const handlePlaybackRateChange = (value: number) => {
    deck.setPlaybackRate(value);
  };

  return (
    <Card className="w-full">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg">Audio Engine Test - Deck {deck.id.replace('deck-', '')}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Track Info */}
        <div className="text-sm">
          {deck.loadedTrack ? (
            <div>
              <p><strong>Track:</strong> {deck.loadedTrack.title || deck.loadedTrack.filename}</p>
              <DeckCurrentTime deck={deck} />
              <p><strong>Playback Rate:</strong> {deck.playbackRate.toFixed(2)}x</p>
              <p><strong>BPM:</strong> {deck.currentBpm.toFixed(1)}</p>
              {/* Audio Graph Visualization */}
              <AudioGraphView audioEngine={deck.audioEngine} />
            </div>
          ) : (
            <p>No track loaded</p>
          )}
        </div>

        {/* Playback Controls */}
        <div className="flex justify-center space-x-2 pt-2">
          <Button
            variant="outline"
            size="icon"
            onClick={handleSeekToStart}
            disabled={!deck.loadedTrack}
          >
            <SkipBack className="h-4 w-4" />
          </Button>
          <Button
            variant="default"
            size="icon"
            onClick={handlePlayPause}
            disabled={!deck.loadedTrack}
          >
            {deck.isPlaying ? (
              <Pause className="h-4 w-4" />
            ) : (
              <Play className="h-4 w-4" />
            )}
          </Button>
        </div>

        {/* EQ Controls - Show 3 or 4 bands based on settings */}
        <div className="flex justify-between pt-4">
          <div className="flex flex-col items-center">
            <Knob
              min={minEQValue}
              max={12}
              value={deck.lowEQ ?? 0}
              defaultValue={0}
              stopValue={0}
              hasStop={true}
              step={0.1}
              size="sm"
              label="LOW"
              color="var-destructive" // More red/orange color for bass frequencies
              onChange={handleLowEQChange}
              disabled={!deck.loadedTrack}
            />
          </div>

          {is4BandMode ? (
            // 4-band mode: show mid-lo and mid-hi
            <>
              <div className="flex flex-col items-center">
                <Knob
                  min={minEQValue}
                  max={12}
                  value={deck.midLoEQ ?? 0}
                  defaultValue={0}
                  stopValue={0}
                  hasStop={true}
                  step={0.1}
                  size="sm"
                  label="MID-LO"
                  color="bg-amber-500" // Amber color for mid-lo frequencies
                  onChange={handleMidLoEQChange}
                  disabled={!deck.loadedTrack}
                />
              </div>

              <div className="flex flex-col items-center">
                <Knob
                  min={minEQValue}
                  max={12}
                  value={deck.midHiEQ ?? 0}
                  defaultValue={0}
                  stopValue={0}
                  hasStop={true}
                  step={0.1}
                  size="sm"
                  label="MID-HI"
                  color="bg-lime-500" // Lime color for mid-hi frequencies
                  onChange={handleMidHiEQChange}
                  disabled={!deck.loadedTrack}
                />
              </div>
            </>
          ) : (
            // 3-band mode: show mid
            <div className="flex flex-col items-center">
              <Knob
                min={minEQValue}
                max={12}
                value={deck.midEQ ?? 0}
                defaultValue={0}
                stopValue={0}
                hasStop={true}
                step={0.1}
                size="sm"
                label="MID"
                color="var-chart-4" // Yellow/green for mid frequencies
                onChange={handleMidEQChange}
                disabled={!deck.loadedTrack}
              />
            </div>
          )}

          <div className="flex flex-col items-center">
            <Knob
              min={minEQValue}
              max={12}
              value={deck.highEQ ?? 0}
              defaultValue={0}
              stopValue={0}
              hasStop={true}
              step={0.1}
              size="sm"
              label="HIGH"
              color="var-chart-2" // Blue for high frequencies
              onChange={handleHighEQChange}
              disabled={!deck.loadedTrack}
            />
          </div>
        </div>

        {/* Volume Control - replaced with vertical Fader */}
        <div className="flex justify-center pt-4 gap-4">
          <Fader
            min={0}
            max={1}
            value={deck.volume ?? 1}
            defaultValue={1}
            step={0.01}
            size="md"
            label="VOLUME"
            unit="%"
            color="bg-chart-2"
            orientation="vertical"
            onChange={handleVolumeChange}
            disabled={!deck.loadedTrack}
            decimals={2}
            className="h-48"
          />
          {/* Audio Level Meter */}
          <AudioLevelMeters
            analyzer={(() => { try { return deck.audioEngine?.getAnalyzer() || null; } catch { return null; } })()}
            horizontal={false}
          />
        </div>

        {/* Playback Rate Control */}
        <div className="space-y-2 pt-4">
          <div className="flex justify-between">
            <span className="text-sm">Playback Rate</span>
            <span className="text-sm">{deck.playbackRate.toFixed(2)}x</span>
          </div>
          <Fader
            min={0.8}
            max={1.2}
            value={deck.playbackRate}
            defaultValue={1.0}
            step={0.01}
            size="sm"
            color="bg-chart-4"
            orientation="horizontal"
            onChange={handlePlaybackRateChange}
            disabled={!deck.loadedTrack}
            decimals={2}
            unit="x"
            hasStop={true}
            stopValue={1.0}
          />

          {/* Master Tempo Checkbox */}
          <div className="flex items-center space-x-2 pt-2">
            <Checkbox
              id={`mt-checkbox-${deck.id}`}
              checked={deck.masterTempo}
              onCheckedChange={(checked: boolean | 'indeterminate') => deck.setMasterTempo(checked === true)}
              disabled={!deck.loadedTrack}
            />
            <label
              htmlFor={`mt-checkbox-${deck.id}`}
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              MT (Master Tempo)
            </label>
          </div>
        </div>
      </CardContent>
    </Card>
  );
});

export default AudioEngineTest;
